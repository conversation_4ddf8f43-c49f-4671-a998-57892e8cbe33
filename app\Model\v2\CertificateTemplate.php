<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Model;

class CertificateTemplate extends Model
{
    protected $table = 'certificate_templates';

    protected $fillable = [
        'name',
        'json_data',
        'html_data',
        'paper_size',
        'orientation',
        'thumbnail',
        'is_default',
        'metadata',
        'certificate_number_formate_id',
        'type',
        'template_type',
    ];

    protected $casts = [
        'json_data' => 'array',
        // 'metadata' => 'array',
    ];

    const TEMPLATE_TYPE_CERTIFICATE = 'certificate';
    const TEMPLATE_TYPE_STUDENT_CARD = 'student-id';

    // Default status constants
    const DEFAULT_STATUS_NO = '0';
    const DEFAULT_STATUS_YES = '1';

    public function setAsDefault()
    {
        // First, remove default status from all other templates of the same type
        static::where('template_type', $this->template_type)
            ->where('id', '!=', $this->id)
            ->update(['is_default' => self::DEFAULT_STATUS_NO]);

        // Set this template as default
        $this->update(['is_default' => self::DEFAULT_STATUS_YES]);
    }

    public static function getDefaultTemplate($templateType)
    {
        return static::where('template_type', $templateType)
            ->where('is_default', self::DEFAULT_STATUS_YES)
            ->first();
    }

    public function isDefault()
    {
        return $this->is_default === self::DEFAULT_STATUS_YES;
    }

    public function isLastOfType()
    {
        return static::where('template_type', $this->template_type)->count() === 1;
    }
}
