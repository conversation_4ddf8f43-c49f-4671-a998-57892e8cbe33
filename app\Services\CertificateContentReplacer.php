<?php

namespace App\Services;

use DOMDocument;
use DOMXPath;
use Illuminate\Support\Str;

class CertificateContentReplacer
{
    protected array $data;

    const ALLOWED_TAGS = [
        '[unit.list]',
        '[unit.list.with.result]',
        '[unit.interim.table]',
    ];

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function replace(string $template): string
    {
        // First replace QR code if present
        $template = $this->replaceQrCode($template);

        // Replace image attributes (college_logo, profile_picture)
        $template = $this->replaceImageAttributes($template);

        // Match all placeholders like [student.fullname], [unit.list]
        return preg_replace_callback('/\[(.*?)\]/', function ($matches) {
            $key = $matches[1];

            // Support table tags like unit.list, unit.list.with.result, etc.
            if (Str::startsWith($key, 'unit.')) {
                return $this->renderUnitTable($key);
            }

            // Get nested value from data using dot notation
            return data_get($this->data, $key, '');
        }, $template);
    }

    protected function replaceQrCode(string $template): string
    {
        if (! isset($this->data['qr_code_src'])) {
            return $template;
        }

        // Find the div with id="qrImage" and replace the img src inside it
        return preg_replace(
            '/(<div[^>]*id="qrImage"[^>]*>.*?<img[^>]*src=")[^"]*("[^>]*>.*?<\/div>)/s',
            '$1'.$this->data['qr_code_src'].'$2',
            $template
        );
    }

    protected function replaceImageAttributes(string $template): string
    {
        // Replace standalone [college_logo] placeholders with complete img tags
        if (isset($this->data['college_logo']) && ! empty($this->data['college_logo'])) {
            $template = str_replace(
                '[college_logo]',
                '<img src="'.$this->data['college_logo'].'" alt="College Logo" style="max-width: 50%; height: auto;" />',
                $template
            );
        }

        // Replace standalone [profile_picture] placeholders with complete img tags
        if (isset($this->data['profile_picture']) && ! empty($this->data['profile_picture'])) {
            $template = str_replace(
                '[profile_picture]',
                '<img src="'.$this->data['profile_picture'].'" alt="Profile Picture" style="max-width: 25%; height: auto;" />',
                $template
            );
        }

        return $template;
    }

    protected function renderUnitTable(string $key): string
    {
        $units = $this->data['unit']['list'] ?? [];
        $unitsTrascript = $this->data['unit']['trascript'] ?? [];
        $gradeData = $this->data['unit']['otherData']['gradeData'] ?? [];
        $gradePointData = $this->data['unit']['otherData']['gradePointData'] ?? [];

        if ($key === 'unit.list.with.result') {
            return $this->renderTable($units, ['Code', 'Name', 'Result', 'Year'], function ($unit) {
                return [
                    $unit['code'] ?? '',
                    $unit['name'] ?? '',
                    $unit['result'] ?? '',
                    $unit['year'] ?? '',
                ];
            });
        }

        if ($key === 'unit.interim.table') {
            return $this->renderSemesterWiseTranscript($unitsTrascript, $gradeData, $gradePointData);
        }

        // Default unit list
        return $this->renderTable($units, ['Unit Code', 'Unit Name'], function ($unit) {
            return [
                $unit['code'] ?? '',
                $unit['name'] ?? '',
            ];
        });
    }

    protected function renderSemesterWiseTranscript(array $semWiseChunkDatas, array $gradeData, array $gradePointData): string
    {
        $html = '';

        foreach ($semWiseChunkDatas as $index => $semWiseDatas) {
            foreach ($semWiseDatas['data'] as $semWiseData) {
                $html .= $this->renderSemesterTable($semWiseData, $gradeData, $gradePointData);
            }
            // Add page-break if not the last chunk
            if ($index < count($semWiseChunkDatas) - 1) {
                $html .= '</div><div class="page-break"></div><div class="page-break-table">';
            }
        }

        return $html;
    }

    protected function renderSemesterTable(array $semWiseData, array $gradeData, array $gradePointData): string
    {
        $data = $semWiseData['data'];
        $semesterName = $semWiseData['semester_name'];

        $html = '<table width="100%">';
        $html .= $this->renderTableHeader();
        $html .= '<tbody>';

        if (empty($data)) {
            $html .= $this->renderNoRecordsRow();
        } else {
            $html .= $this->renderSemesterRows($data, $semesterName, $gradeData, $gradePointData);
        }

        $html .= '</tbody></table>';

        return $html;
    }

    protected function renderTableHeader(): string
    {
        return '<thead>
            <tr style="background-color: #f2f2f2;">
                <th>Semester</th>
                <th>Unit Code</th>
                <th>Unit Name</th>
                <th>Credit Point</th>
                <th>Marks</th>
                <th>Grade</th>
                <th>Grade Point</th>
            </tr>
        </thead>';
    }

    protected function renderNoRecordsRow(): string
    {
        return '<tr><td colspan="7">No Records Found</td></tr>';
    }

    protected function renderSemesterRows($data, string $semesterName, array $gradeData, array $gradePointData): string
    {
        $html = '';
        $averageGPA = 0;
        $sumOfCreditPoint = 0;

        // Convert Collection to array if needed
        $dataArray = is_array($data) ? $data : $data->toArray();

        foreach ($dataArray as $index => $enrollData) {
            if (! $this->isValidEnrollmentData($enrollData)) {
                $html .= $this->renderNoRecordsRow();

                continue;
            }

            $html .= $this->renderEnrollmentRow($enrollData, $index, $semesterName, $gradeData, $gradePointData, count($dataArray));

            $gradePoint = $gradePointData[$enrollData['mark_outcome']] ?? 0;
            $creditPoint = $enrollData['subject']['credit_point'] ?? 0;
            $averageGPA += ($gradePoint * $creditPoint);
            $sumOfCreditPoint += $creditPoint;
        }

        $gpa = $sumOfCreditPoint ? number_format($averageGPA / $sumOfCreditPoint, 2) : 0;
        $html .= $this->renderGPARow($gpa);

        return $html;
    }

    protected function isValidEnrollmentData(array $enrollData): bool
    {
        return isset($enrollData['unit']) && isset($enrollData['unit']['unit_name']);
    }

    protected function renderEnrollmentRow(array $enrollData, int $index, string $semesterName, array $gradeData, array $gradePointData, int $totalRows): string
    {
        $html = '<tr>';

        if ($index === 0) {
            $html .= '<td style="min-height:40px;height:auto;line-height:1.4em;vertical-align:top;" rowspan="'.$totalRows.'">'.$semesterName.'</td>';
        }

        $grade = $gradeData[$enrollData['mark_outcome']] ?? '';
        $gradePoint = $gradePointData[$enrollData['mark_outcome']] ?? 0;

        $html .= '<td>'.($enrollData['unit']['unit_code'] ?? '').'</td>';
        $html .= '<td>'.($enrollData['unit']['unit_name'] ?? '').'</td>';
        $html .= '<td>'.($enrollData['subject']['credit_point'] ?? '').'</td>';
        $html .= '<td>'.($enrollData['marks'] ?? '').'</td>';
        $html .= '<td>'.$grade.'</td>';
        $html .= '<td>'.$gradePoint.'</td>';
        $html .= '</tr>';

        return $html;
    }

    protected function renderGPARow(float $gpa): string
    {
        return '<tr>
            <td colspan="4"></td>
            <td colspan="3">Grade Point Average (GPA): '.$gpa.'</td>
        </tr>';
    }

    protected function renderTable(array $chunkItems, array $headers, \Closure $rowCallback): string
    {
        $html = '';

        if (empty($chunkItems)) {
            $html .= '<table width="100%"><thead><tr>';
            foreach ($headers as $header) {
                $html .= "<th>{$header}</th>";
            }
            $html .= '</tr></thead><tbody>';
            $html .= '<tr><td colspan="'.count($headers).'">No Records Found</td></tr>';
            $html .= '</tbody></table>';

            return $html;
        }

        $totalChunks = count($chunkItems);

        foreach ($chunkItems as $index => $items) {
            $html .= '<table width="100%"><thead><tr>';
            foreach ($headers as $header) {
                $html .= "<th>{$header}</th>";
            }
            $html .= '</tr></thead><tbody>';

            foreach ($items as $item) {
                $row = $rowCallback($item);
                $html .= '<tr>';
                foreach ($row as $col) {
                    $html .= "<td>{$col}</td>";
                }
                $html .= '</tr>';
            }

            $html .= '</tbody></table>';

            // Add page-break if not the last chunk
            if ($index < $totalChunks - 1) {
                $html .= '</div><div class="page-break"></div><div class="page-break-table">';
            }
        }

        return $html;
    }

    public function extractStyles(string $html): array
    {
        $dom = new DOMDocument;
        libxml_use_internal_errors(true); // Suppress invalid HTML warnings
        $dom->loadHTML($html);
        libxml_clear_errors();

        $xpath = new DOMXPath($dom);
        $tagStyles = [];

        foreach (self::ALLOWED_TAGS as $tag) {
            // Search for elements containing the tag text
            $elements = $xpath->query("//*[contains(text(), '".$tag."')]");

            foreach ($elements as $el) {
                $updatedStyle = '';

                if ($el->hasAttribute('style')) {
                    $style = $el->getAttribute('style');
                    // Remove 'top:...;' using regex
                    $updatedStyle = preg_replace('/top\s*:\s*[^;]+;?/i', '', $style);
                }

                // Save the style for the tag
                $tagStyles[$tag] = $updatedStyle;

                // Break if you only want the first match for each tag
                break;
            }
        }

        return $tagStyles;
    }

    public function makeFontLink(string $metadata): string
    {
        $decodeJson = json_decode($metadata, true);

        $fonts = $decodeJson['fonts'] ?? [];
        // Step 1: Replace space with `+` in each font
        $fontsFormatted = array_map(function ($font) {
            return str_replace(' ', '+', $font);
        }, $fonts);

        // Step 2: Implode into a single string
        $fontQuery = implode('&family=', $fontsFormatted);

        return 'https://fonts.googleapis.com/css2?family='.$fontQuery.'&display=swap';
    }
}
