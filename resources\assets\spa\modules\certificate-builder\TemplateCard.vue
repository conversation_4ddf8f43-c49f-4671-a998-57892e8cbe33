<template>
    <div class="group flex flex-col gap-3">
        <div
            class="relative overflow-hidden rounded-md bg-gray-100"
            :class="type === 'student-id' ? 'aspect-[320/204]' : 'aspect-[3/4]'"
        >
            <img
                :src="data?.thumbnail"
                class="image-item h-full w-full object-contain transition-all duration-300 group-hover:scale-105"
                alt="Certificate Image"
            />
            <div
                class="invisible absolute inset-0 flex h-full w-full items-center justify-center gap-4 bg-gray-800/40 opacity-0 group-hover:visible group-hover:opacity-100"
            >
                <Button :size="'sm'" @click="handleEdit">Edit</Button>
                <Button variant="secondary" :size="'sm'" @click="handlePreview($event, data)"
                    >Preview</Button
                >
            </div>
        </div>
        <div class="flex items-start justify-between">
            <div class="flex flex-col gap-1">
                <h4 class="text-base font-medium">
                    {{ data?.name.split('_').join(' ') }}
                </h4>
                <div
                    v-if="data?.template_type === 'student-id' && data?.is_default === 1"
                    class="flex items-center gap-1"
                >
                    <span
                        class="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800"
                    >
                        <svg class="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                clip-rule="evenodd"
                            />
                        </svg>
                        Default Card
                    </span>
                </div>
            </div>
            <Popover
                :open="openIndex === index"
                @toggle="openIndex = openIndex === index ? null : index"
                @close="openIndex = null"
            >
                <template #picker>
                    <Button variant="icon" class="k-link h-auto" @click="openMenu = !openMenu"
                        ><IconMoreHorizontal24Regular class="h-4 w-4"
                    /></Button>
                </template>
                <template #popup>
                    <ul class="min-w-[120px] py-1">
                        <li>
                            <button
                                class="inline-flex w-full items-center gap-2 px-3 py-1 text-left hover:bg-gray-100"
                                @click="handleEdit"
                            >
                                <IconPen24Regular class="h-4 w-4" />
                                <span>Edit</span>
                            </button>
                        </li>
                        <li>
                            <button
                                class="inline-flex w-full items-center gap-2 px-3 py-1 text-left hover:bg-gray-100"
                                @click="handlePreview"
                            >
                                <IconEye24Regular class="h-4 w-4" />
                                <span>Preview</span>
                            </button>
                        </li>
                        <li v-if="data?.template_type === 'student-id' && data?.is_default !== 1">
                            <button
                                class="inline-flex w-full items-center gap-2 px-3 py-1 text-left hover:bg-gray-100"
                                @click="handleSetAsDefault"
                            >
                                <IconCheckmark24Regular class="h-4 w-4" />
                                <span>Set as Default</span>
                            </button>
                        </li>
                        <li>
                            <button
                                class="inline-flex w-full items-center gap-2 px-3 py-1 text-left hover:bg-gray-100"
                                @click="handleDelete"
                            >
                                <IconDelete24Regular class="h-4 w-4" />
                                <span>Delete</span>
                            </button>
                        </li>
                    </ul>
                </template>
            </Popover>
        </div>
    </div>
</template>
<script setup>
import { ref } from 'vue';
import Button from '@spa/components/Buttons/Button.vue';
import Popover from '@spa/components/Popover/Popover.vue';
import {
    IconMoreHorizontal24Regular,
    IconEye24Regular,
    IconPen24Regular,
    IconDelete24Regular,
    IconCheckmark24Regular,
} from '@iconify-prerendered/vue-fluent';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    type: {
        type: String,
        default: 'certificate',
    },
});
const emit = defineEmits(['delete', 'edit', 'preview', 'setAsDefault']);
const openPreviewPopup = ref(false);
const openMenu = ref(false);
const openIndex = ref(null);

const handlePreview = (e, item) => {
    emit('preview');
};

const closePreviewPopup = () => {
    openPreviewPopup.value = false;
};

const handleEdit = () => {
    emit('edit');
};

const handleDelete = () => {
    emit('delete');
};

const handleSetAsDefault = () => {
    emit('setAsDefault');
    openIndex.value = null; // Close the menu
};

const handleClose = (event) => {
    console.log('close event', event);
    openMenu.value = false;
};
</script>
<style lang=""></style>
